<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Session.Type Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 WebRTC Session.Type Detection Test</h1>
        <p>This test page allows you to trigger WebRTC connections and capture session.type injection for debugging.</p>
        
        <div>
            <button onclick="testWebRTCConnection()">🚀 Test WebRTC Connection</button>
            <button onclick="testEphemeralToken()">🔑 Test Ephemeral Token</button>
            <button onclick="clearLogs()">🗑️ Clear Logs</button>
        </div>

        <div id="status" class="log">Ready to test...</div>
        <div id="logs" class="log"></div>

        <h3>Instructions:</h3>
        <ol>
            <li>Open browser Developer Tools (F12) and go to Console tab</li>
            <li>Click "Test WebRTC Connection" to trigger the connection</li>
            <li>Watch the console for detailed logs with session.type detection</li>
            <li>Look for lines marked with 🚨 indicating session.type injection source</li>
        </ol>
    </div>

    <script type="module">
        let logElement = document.getElementById('logs');
        let statusElement = document.getElementById('status');

        function updateStatus(message, type = 'info') {
            statusElement.textContent = message;
            statusElement.className = `log ${type}`;
        }

        function addLog(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logLine = `[${timestamp}] ${message}\n`;
            logElement.textContent += logLine;
            logElement.scrollTop = logElement.scrollHeight;
            
            // Also log to console for detailed debugging
            console.log(message);
        }

        window.clearLogs = function() {
            logElement.textContent = '';
            updateStatus('Logs cleared');
        };

        window.testEphemeralToken = async function() {
            updateStatus('Testing ephemeral token creation...', 'warning');
            addLog('🔑 Creating ephemeral token...');
            
            try {
                const response = await fetch('/api/voice/ephemeral-token?model=gpt-realtime&voice=alloy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${await response.text()}`);
                }

                const data = await response.json();
                addLog(`✅ Token created: ${data.id}`, 'success');
                updateStatus('Ephemeral token created successfully', 'success');
                return data.client_secret?.value || data.client_secret;
            } catch (error) {
                addLog(`❌ Token creation failed: ${error.message}`, 'error');
                updateStatus('Ephemeral token creation failed', 'error');
                return null;
            }
        };

        window.testWebRTCConnection = async function() {
            updateStatus('Testing WebRTC connection...', 'warning');
            addLog('🚀 Starting WebRTC connection test...');
            
            try {
                // Step 1: Get ephemeral token
                const token = await testEphemeralToken();
                if (!token) {
                    throw new Error('Failed to get ephemeral token');
                }

                // Step 2: Import and create voice client
                addLog('📦 Importing voice client...');
                const { createModernRealtimeVoiceClient } = await import('/src/lib/ModernRealtimeVoiceClient.ts');
                
                addLog('🎤 Creating voice client with enhanced logging...');
                const client = createModernRealtimeVoiceClient(
                    {
                        ephemeralToken: token,
                        transport: 'webrtc',
                        enableDebugLogs: true, // This will trigger our enhanced logging
                        model: 'gpt-realtime',
                        voice: 'alloy'
                    },
                    {
                        onConnected: () => {
                            addLog('✅ Voice client connected!', 'success');
                            updateStatus('WebRTC connection successful!', 'success');
                        },
                        onDisconnected: () => {
                            addLog('❌ Voice client disconnected');
                        },
                        onError: (error) => {
                            addLog(`🚨 Voice client error: ${error}`, 'error');
                            updateStatus(`Connection error: ${error}`, 'error');
                        }
                    }
                );

                // Step 3: Attempt connection (this will trigger our enhanced fetch logging)
                addLog('🔌 Attempting WebRTC connection...');
                addLog('👀 Watch console for detailed session.type detection logs!');
                
                const connected = await client.connect();
                
                if (connected) {
                    addLog('✅ Connection established successfully!', 'success');
                    updateStatus('Connected! Check console for session.type analysis', 'success');
                    
                    // Clean disconnect after a moment
                    setTimeout(async () => {
                        await client.disconnect();
                        addLog('🔌 Disconnected cleanly');
                    }, 2000);
                } else {
                    addLog('❌ Connection failed', 'error');
                    updateStatus('Connection failed', 'error');
                }
                
            } catch (error) {
                addLog(`🚨 Test failed: ${error.message}`, 'error');
                updateStatus(`Test failed: ${error.message}`, 'error');
                console.error('WebRTC test error:', error);
            }
        };

        // Initialize
        updateStatus('Test page loaded - ready to debug session.type injection');
        addLog('🔍 WebRTC Session.Type Detection Test Ready');
        addLog('💡 Click "Test WebRTC Connection" and watch the console for detailed logs');
    </script>
</body>
</html>