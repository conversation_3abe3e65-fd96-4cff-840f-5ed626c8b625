import type { VercelRequest, VercelResponse } from '@vercel/node';

// Issues an ephemeral OpenAI Realtime session token.
// Requires OPENAI_API_KEY in server environment. Never expose this to the client.
export default async function handler(req: VercelRequest, res: VercelResponse) {
  if (req.method !== 'POST' && req.method !== 'GET') {
    res.setHeader('Allow', 'GET, POST');
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const apiKey = process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_PRIMARY;
  if (!apiKey) {
    return res.status(200).json({ error: 'OPENAI_API_KEY not configured' });
  }

  const model = (req.query.model as string) || 'gpt-4o-realtime-preview-2024-12-17';
  const voice = (req.query.voice as string) || 'alloy';

  try {
    const response = await fetch('https://api.openai.com/v1/realtime/sessions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        voice,
        // TTL in seconds; ephemeral token is short-lived
        expires_in: 300,
      }),
    });

    const data = await response.json();
    if (!response.ok) {
      console.error('Failed to mint ephemeral token:', data);
      return res.status(200).json({ error: data?.error || 'Failed to mint token' });
    }

    // Return full response so client can use client_secret.value
    return res.status(200).json(data);
  } catch (error) {
    console.error('Ephemeral token error:', error);
    return res.status(200).json({ error: 'Network error' });
  }
}

