import type { VercelRequest, VercelResponse } from '@vercel/node';

/**
 * Proxy endpoint for OpenAI Realtime WebRTC calls
 * Routes /api/openai/realtime/calls/* to https://api.openai.com/v1/realtime/calls/*
 * Handles SDP offer/answer exchange for WebRTC connections
 */
export default async function handler(req: VercelRequest, res: VercelResponse) {
  try {
    // Handle CORS preflight requests
    if (req.method === 'OPTIONS') {
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept');
      res.setHeader('Access-Control-Max-Age', '86400');
      return res.status(200).end();
    }

    // Only allow POST method for SDP exchange
    if (req.method !== 'POST') {
      res.setHeader('Allow', 'POST, OPTIONS');
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Extract the path and query parameters
    const { path: segments, ...query } = req.query;
    const pathSegments = Array.isArray(segments) ? segments : [segments].filter(Boolean);
    const targetPath = pathSegments.join('/');
    
    // Build the target URL
    const queryString = new URLSearchParams();
    Object.entries(query).forEach(([key, value]) => {
      if (value) {
        queryString.append(key, Array.isArray(value) ? value[0] : value);
      }
    });
    
    const targetUrl = `https://api.openai.com/v1/realtime/calls${targetPath ? `/${targetPath}` : ''}${queryString.toString() ? `?${queryString.toString()}` : ''}`;

    // Get authorization header from the request
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Missing or invalid authorization header' });
    }

    // Prepare headers for the upstream request
    const upstreamHeaders: Record<string, string> = {
      'Authorization': authHeader,
      'User-Agent': 'OpenAI-Realtime-Proxy/1.0',
    };

    // Handle Content-Type based on the request
    const contentType = req.headers['content-type'];
    if (contentType) {
      upstreamHeaders['Content-Type'] = contentType;
    }

    // Handle Accept header for SDP responses
    const acceptHeader = req.headers.accept;
    if (acceptHeader) {
      upstreamHeaders['Accept'] = acceptHeader;
    }

    // Get the request body (SDP offer for WebRTC)
    let body: string | undefined;
    if (req.body) {
      body = typeof req.body === 'string' ? req.body : JSON.stringify(req.body);
    }

    // Make the proxied request to OpenAI
    const response = await fetch(targetUrl, {
      method: 'POST',
      headers: upstreamHeaders,
      body,
    });

    // Handle the response
    if (!response.ok) {
      const errorText = await response.text().catch(() => '');
      console.error(`OpenAI Realtime API error: ${response.status} ${response.statusText}`, errorText);
      
      let errorMessage;
      try {
        const errorJson = JSON.parse(errorText);
        errorMessage = errorJson?.error?.message || errorJson?.message || 'Unknown error';
      } catch {
        errorMessage = errorText || 'Unknown error';
      }
      
      return res.status(response.status).json({ 
        error: `OpenAI API error: ${errorMessage}` 
      });
    }

    // Get response content type
    const responseContentType = response.headers.get('content-type') || '';
    
    // Handle SDP responses (text/plain or application/sdp)
    if (responseContentType.includes('sdp') || responseContentType.includes('text/plain')) {
      const sdpText = await response.text();
      
      // Validate SDP format - must start with v= line
      if (!sdpText.trim().startsWith('v=')) {
        console.error('Invalid SDP response received:', sdpText.substring(0, 200));
        return res.status(502).json({ 
          error: 'Invalid SDP response from OpenAI API' 
        });
      }
      
      // Set appropriate headers for SDP response
      res.setHeader('Content-Type', 'application/sdp');
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept');
      
      return res.status(200).send(sdpText);
    }
    
    // Handle JSON responses
    if (responseContentType.includes('application/json')) {
      const jsonData = await response.json();
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept');
      
      return res.status(response.status).json(jsonData);
    }
    
    // Handle other response types
    const responseText = await response.text();
    res.setHeader('Content-Type', responseContentType);
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept');
    
    return res.status(response.status).send(responseText);

  } catch (error) {
    console.error('Realtime calls proxy error:', error);
    return res.status(500).json({ 
      error: 'Internal proxy error',
      details: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Handle preflight OPTIONS requests for CORS
 */
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
};