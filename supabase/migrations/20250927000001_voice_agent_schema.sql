-- ================================================================
-- VOICE AGENT CORE SCHEMA
-- Creates conversation/session tables required by the voice agent
-- ================================================================

BEGIN;

CREATE EXTENSION IF NOT EXISTS "pgcrypto";

CREATE TABLE IF NOT EXISTS voice_conversation_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id),
    status TEXT NOT NULL DEFAULT 'idle',
    started_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc', now()),
    last_active_at TIMESTAMPTZ,
    total_interactions INTEGER NOT NULL DEFAULT 0,
    average_response_time INTEGER NOT NULL DEFAULT 0,
    metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc', now()),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc', now())
);

CREATE TABLE IF NOT EXISTS voice_conversation_turns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES voice_conversation_sessions(id) ON DELETE CASCADE,
    timestamp TIMESTAMPTZ NOT NULL,
    user_input TEXT,
    agent_response TEXT,
    intent_type TEXT,
    intent_action TEXT,
    confidence NUMERIC(5,2),
    latency INTEGER,
    entities JSONB NOT NULL DEFAULT '[]'::jsonb,
    actions_taken JSONB NOT NULL DEFAULT '[]'::jsonb,
    metadata JSONB NOT NULL DEFAULT '{}'::jsonb
);

CREATE INDEX IF NOT EXISTS idx_voice_turns_session_id
    ON voice_conversation_turns(session_id, timestamp DESC);

CREATE TABLE IF NOT EXISTS voice_agent_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    preferred_voice TEXT,
    speaking_speed NUMERIC(4,2) NOT NULL DEFAULT 1.00,
    noise_reduction_level TEXT,
    confirmation_required BOOLEAN NOT NULL DEFAULT false,
    verbosity_level TEXT,
    language TEXT,
    shortcuts JSONB NOT NULL DEFAULT '[]'::jsonb,
    metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc', now()),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT timezone('utc', now()),
    UNIQUE(user_id)
);

CREATE TABLE IF NOT EXISTS voice_performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES voice_conversation_sessions(id) ON DELETE SET NULL,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT timezone('utc', now()),
    latency_total INTEGER,
    latency_recognition INTEGER,
    latency_processing INTEGER,
    latency_function INTEGER,
    latency_synthesis INTEGER,
    accuracy_recognition NUMERIC(5,2),
    accuracy_intent NUMERIC(5,2),
    accuracy_entity NUMERIC(5,2),
    tokens_used INTEGER,
    functions_executed INTEGER,
    errors_encountered INTEGER,
    metadata JSONB NOT NULL DEFAULT '{}'::jsonb
);

CREATE TABLE IF NOT EXISTS voice_error_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES voice_conversation_sessions(id) ON DELETE SET NULL,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT timezone('utc', now()),
    error_type TEXT,
    error_code TEXT,
    message TEXT,
    context JSONB NOT NULL DEFAULT '{}'::jsonb,
    resolved BOOLEAN NOT NULL DEFAULT false,
    resolution TEXT
);

CREATE INDEX IF NOT EXISTS idx_voice_errors_session_id
    ON voice_error_logs(session_id, timestamp DESC);

-- Create the trigger function for updating updated_at timestamps
CREATE OR REPLACE FUNCTION trigger_set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc', now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'voice_conversation_sessions_set_updated_at') THEN
        CREATE TRIGGER voice_conversation_sessions_set_updated_at
        BEFORE UPDATE ON voice_conversation_sessions
        FOR EACH ROW EXECUTE FUNCTION trigger_set_updated_at();
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'voice_agent_configs_set_updated_at') THEN
        CREATE TRIGGER voice_agent_configs_set_updated_at
        BEFORE UPDATE ON voice_agent_configs
        FOR EACH ROW EXECUTE FUNCTION trigger_set_updated_at();
    END IF;
END $$;

ALTER TABLE voice_conversation_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_conversation_turns ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_agent_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_error_logs ENABLE ROW LEVEL SECURITY;

COMMIT;
