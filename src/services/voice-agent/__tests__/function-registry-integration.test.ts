import { beforeEach, describe, expect, it, vi } from 'vitest';

const temperatureHandler = vi.fn(async (args: any, context: any) => {
  return {
    success: true,
    data: {
      readings: [
        {
          sensorId: args.sensorId ?? 'sensor-001',
          recordedAt: '2025-09-27T12:00:00.000Z',
          temperature: 38,
        },
      ],
      metadata: {
        requestedHours: args.hours ?? 24,
        sessionId: context.sessionId,
      },
    },
    message: 'Temperature summary ready',
    formattedForVoice: 'Sensor sensor-001 is at 38°F with no alerts.',
  };
});

const inventoryHandler = vi.fn(async () => ({
  success: true,
  data: { items: [] },
  message: 'Inventory ok',
  formattedForVoice: 'Inventory summary ready.',
}));

const orderHandler = vi.fn(async () => ({
  success: true,
  data: { orders: [] },
  message: 'Orders ok',
  formattedForVoice: 'Orders summary ready.',
}));

const systemStatusHandler = vi.fn(async () => ({
  success: true,
  data: { status: 'operational' },
  message: 'System healthy',
  formattedForVoice: 'System is healthy.',
}));

vi.mock('../config', () => ({
  config: {
    supabase: {
      url: 'http://localhost:54321',
      anonKey: 'test-anon-key',
    },
    openai: {
      apiKey: 'test',
      model: 'gpt-4o-realtime-preview',
      voice: 'alloy',
      temperature: 0.7,
      maxTokens: 1024,
    },
  },
}));

vi.mock('../utils/logger', () => ({
  logger: {
    child: () => ({
      info: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    }),
    info: vi.fn(),
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock('../PerformanceOptimizer', () => ({
  performanceOptimizer: {
    optimizeQuery: vi.fn((cacheKey: string, queryFn: () => Promise<any>, options: any) => {
      return queryFn();
    }),
    addConnection: vi.fn(),
    removeConnection: vi.fn(),
    getCurrentMetrics: vi.fn(() => ({ responseTime: 120, activeConnections: 1 })),
    getCacheStats: vi.fn(() => ({ hitRate: 0.95 })),
  },
}));

vi.mock('../ErrorRecoveryManager', () => ({
  errorRecoveryManager: {
    handleError: vi.fn(() => null),
  },
}));

vi.mock('../AuthenticationService', () => ({
  AuthenticationService: vi.fn(() => ({
    verifyToken: vi.fn(),
    hasPermission: vi.fn(),
    requirePermission: vi.fn(),
  })),
}));

vi.mock('../functions/InventoryFunctions', () => ({
  InventoryFunctions: class {
    getInventoryLevels = inventoryHandler;
    searchProducts = inventoryHandler;
    createInventoryEvent = inventoryHandler;
  },
}));

vi.mock('../functions/OrderFunctions', () => ({
  OrderFunctions: class {
    getRecentOrders = orderHandler;
    getOrderStatus = orderHandler;
  },
}));

vi.mock('../functions/TemperatureFunctions', () => ({
  TemperatureFunctions: class {
    getTemperatureReadings = temperatureHandler;
  },
}));

vi.mock('../../modules/conversation-management/functions/system', () => ({
  systemFunctions: {
    system_status: {
      name: 'system_status',
      description: 'Return system health snapshot',
      parameters: {
        type: 'object',
        properties: {},
      },
      permissions: ['system:read'],
      handler: systemStatusHandler,
    },
  },
}));

import { FunctionRegistry } from '../functions/FunctionRegistry';
import { performanceOptimizer } from '../PerformanceOptimizer';

describe('FunctionRegistry integration harness', () => {
  const userContext = {
    userId: 'voice-agent-test-user',
    userRoles: ['manager'],
  };

  let functionRegistry: FunctionRegistry;

  beforeEach(() => {
    vi.clearAllMocks();
    functionRegistry = new FunctionRegistry(userContext);
  });

  it('registers adapter-backed voice agent functions without hitting real services', async () => {
    const functions = await functionRegistry.getAllFunctions();

    expect(functions).toHaveProperty('get_temperature_readings');
    expect(functions).toHaveProperty('get_inventory_levels');
    expect(functions).toHaveProperty('get_recent_orders');
    expect(functions).toHaveProperty('system_status');
  });

  it('delegates temperature execution through optimizer cache layer', async () => {
    const result = await functionRegistry.executeFunction('get_temperature_readings', {
      sensor_id: 'sensor-123',
      hours: 6,
    });

    expect(result.success).toBe(true);
    expect(result.formattedForVoice).toContain('Sensor');
    expect(result.data?.metadata?.requestedHours).toBe(6);

    expect(vi.mocked(performanceOptimizer.optimizeQuery)).toHaveBeenCalledTimes(1);
    const [cacheKey, _handler, options] = vi.mocked(performanceOptimizer.optimizeQuery).mock.calls[0];
    expect(cacheKey).toContain('get_temperature_readings');
    expect(options).toMatchObject({ enableCache: true, cacheTtl: 30000, timeout: 15000 });

    expect(temperatureHandler).toHaveBeenCalledTimes(1);
    const [args, context] = temperatureHandler.mock.calls[0];
    expect(args.sensor_id).toBe('sensor-123');
    expect(context).toMatchObject({ userId: userContext.userId, sessionId: 'current' });
  });

  it('surfaces system status via mocked conversation-management function', async () => {
    const result = await functionRegistry.executeFunction('system_status', {});

    expect(result.success).toBe(true);
    expect(systemStatusHandler).toHaveBeenCalledTimes(1);
    expect(result.data?.status).toBe('operational');
  });
});
